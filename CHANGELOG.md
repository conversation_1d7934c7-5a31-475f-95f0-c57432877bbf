# [0.2.0](https://github.com/ClashStrategic/release-config/compare/v0.1.0...v0.2.0) (2025-08-19)


### Features

* **ci:** add GitHub workflow permissions support ([0e07ef8](https://github.com/ClashStrategic/release-config/commit/0e07ef89c7652f41caab395648ddc0ce07684fdc))
* **lts-version:** Use latest LTS Node.js version in release workflow ([33ce68b](https://github.com/ClashStrategic/release-config/commit/33ce68b9dc9769c78ce114a80116230c6f916044))
* **workflow:** add GitHub Actions workflow generation and setup ([b82f1b3](https://github.com/ClashStrategic/release-config/commit/b82f1b3ebf7dea58a01c7700c1a48951d8887508))
